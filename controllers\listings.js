const Listing = require("../models/listing")

module.exports.index = async (req, res) => {
  const allListings = await Listing.find({});
  res.render("listings/index", { allListings });
}

module.exports.rendernew =  (req, res) => {
  res.render("listings/new");
}

module.exports.showlisting = async (req, res) => {
  const { id } = req.params;
  const listing = await Listing.findById(id)
  .populate({path:"reviews",
    populate:{
      path:"author",
    },
  })
  .populate("owner");
  if (!listing) {
    req.flash("success", "Listing you requested for does not exist");
    return res.redirect("/listings");
  }
  res.render("listings/show", { listing });
}

// module.exports.createlisting = async (req, res) => {
//   let url = req.file.path;
//   let filename = req.file.filename;
//   console.log(url , "...." , filename);
//   const newListing = new Listing(req.body.listing);
//   newListing.owner = req.user._id;
//   newListing.image = {url,filename}; 
//   await newListing.save();
//   req.flash("success", "New listing created!");
//   res.redirect("/listings");
// }

module.exports.createlisting = async (req, res) => {
  console.log("REQ.BODY:", req.body);
  console.log("REQ.FILE:", req.file);
  const newListing = new Listing(req.body.listing);
  newListing.owner = req.user._id;
  if (req.file) { // ✅ CHANGE HERE (wrap in condition)
    newListing.image = {
      url: req.file.path,      // ✅ CHANGE HERE (Cloudinary secure URL)
      filename: req.file.filename
    };
  }
  await newListing.save();
  console.log("SAVED LISTING:", newListing);
  req.flash("success", "New listing created!");
  res.redirect("/listings");
};



// module.exports.renderupdate = async (req, res) => {
//   let { id } = req.params;
//   // let listing = await Listing.findById(id);
//   // if (!listing.owner._id.equals(req.user._id)) {
//   //   req.flash("success","u dont have authority to make cahnges in the listing");
//   //   res.redirect(`/listings/${id}`);
//   // }
//   let listing = await Listing.findByIdAndUpdate(id, { ...req.body.listing });
//   if(typeof req.file !== "undefined"){
//   let url = req.file.path;
//   let filename = req.file.filename;
//   listing.image = {url,filename};
//   req.flash("success", "Listing updated!");
//   res.redirect(`/listings/${id}`);
//   }
// }

module.exports.renderupdate = async (req, res) => {
  let { id } = req.params;
  let listing = await Listing.findByIdAndUpdate(id, { ...req.body.listing });
  if (req.file) {
    listing.image = {
      url: req.file.path,
      filename: req.file.filename
    };
    await listing.save();
  }
  req.flash("success", "Listing updated!");
  res.redirect(`/listings/${id}`);
};


module.exports.renderedit = async (req, res) => {
  const { id } = req.params;
  const listing = await Listing.findById(id);
  if (!listing) throw new ExpressError(404, "Listing not found");
  res.render("listings/edit", { listing });
}

module.exports.destroy = async (req, res) => {
  const { id } = req.params;
  await Listing.findByIdAndDelete(id);
  req.flash("success", "Listing deleted!");
  res.redirect("/listings");
}