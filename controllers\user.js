const User = require("../models/user.js");




module.exports.rendersignup =  (req, res) => {
  res.render("users/signup");
}

module.exports.signup = async (req, res, next) => {
  try {
    let { username, email, password } = req.body;
    console.log("Received signup form:", username, email);

    const newuser = new User({ email, username });
    const registeredUser = await User.register(newuser, password);
    console.log("User registered:", registeredUser);

    req.login(registeredUser, (err) => {
      if (err) {
        console.log("Login error:", err);
        return next(err);
      }
      console.log("User logged in via req.login");

      req.flash("success", "Welcome to WanderLUST");
      let redirectURL =  res.locals.redirectURL||"/listings"; 
       res.redirect(redirectURL);
    });
  } catch (e) {
    console.log("Signup error:", e.message);
    req.flash("error", e.message);
    return res.redirect("/signup");
  }
}

module.exports.renderlogin = (req, res) => {
  res.render("users/login");
}

module.exports.login  =  async (req, res) => {
    try {
      req.flash("success", "Welcome back to WanderLUST");

      const redirectUrl = req.session.redirectUrl || "/listings";
      delete req.session.redirectUrl;

      res.redirect(redirectUrl);
    } catch (err) {
      req.flash("error", "Something went wrong during login.");
      delete req.session.redirectUrl;

      // Don't redirect and then call next; just call next directly
      return next(err);
    }
  }

  module.exports.logout = (req, res, next) => {
  req.logout((err) => {
    if (err) return next(err);
    // Destroy the session completely
    req.flash("success", "You are logged out");
    req.session.destroy(() => {
      res.clearCookie("connect.sid"); // ✅ force-clears the session cookie
      res.redirect("/listings");
    });
  });
}