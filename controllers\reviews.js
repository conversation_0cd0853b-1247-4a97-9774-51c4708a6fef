const Listing = require("../models/listing")
const Review = require("../models/review")



module.exports.createreview = async(req,res)=>{
    let listing = await Listing.findById(req.params.id);
    let newReview = new Review(req.body.review);
      newReview.author = req.user._id;
    listing.reviews.push(newReview);
    await newReview.save();
    await listing.save();
    req.flash("success","new reveiw added");
   // console.log("new reivew saved");
    res.redirect(`/listings/${listing._id}`);
}

module.exports.destroyreview = async(req,res)=>{
  let{id,reviewId} = req.params;
  await Listing.findByIdAndUpdate(id, {$pull:{reviews:reviewId}});
  await Review.findByIdAndDelete(reviewId);
  req.flash("success","reveiw deleted");
  res.redirect(`/listings/${id}`);
}
